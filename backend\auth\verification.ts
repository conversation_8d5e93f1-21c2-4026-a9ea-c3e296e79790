import type { VercelRequest, VercelResponse } from '@vercel/node';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import prisma from '../lib/prisma';
import VerificationService from '../services/verificationService';
import { referralService } from '../services/referralService';

// 加载环境变量
dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

export default async function handler(
  request: VercelRequest,
  response: VercelResponse,
) {
  // 设置CORS头
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  response.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (request.method === 'OPTIONS') {
    return response.status(200).end();
  }

  const verificationService = new VerificationService();

  try {
    // 发送验证码
    if (request.method === 'POST' && request.url?.includes('/send')) {
      const { email, type = 'EMAIL' } = request.body;

      if (!email) {
        return response.status(400).json({ 
          success: false, 
          message: '邮箱地址是必填项' 
        });
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return response.status(400).json({ 
          success: false, 
          message: '请输入有效的邮箱地址' 
        });
      }

      // 获取客户端信息
      const ipAddress = request.headers['x-forwarded-for'] as string || 
                       request.headers['x-real-ip'] as string || 
                       request.connection?.remoteAddress || 
                       'unknown';
      const userAgent = request.headers['user-agent'] || 'unknown';

      const result = await verificationService.sendVerificationCode(
        email,
        type as 'EMAIL' | 'SMS',
        'LOGIN',
        ipAddress,
        userAgent
      );

      return response.status(result.success ? 200 : 400).json(result);
    }

    // 验证验证码并登录
    if (request.method === 'POST' && request.url?.includes('/verify')) {
      const { email, code, type = 'EMAIL', inviteCode } = request.body;

      if (!email || !code) {
        return response.status(400).json({ 
          success: false, 
          message: '邮箱和验证码是必填项' 
        });
      }

      // 验证验证码
      const verifyResult = await verificationService.verifyCode(
        email,
        code,
        type as 'EMAIL' | 'SMS',
        'LOGIN'
      );

      if (!verifyResult.success) {
        return response.status(400).json(verifyResult);
      }

      // 验证码正确，查找或创建用户
      let user = await prisma.user.findUnique({
        where: { email },
        include: { balance: true }
      });

      // 如果用户不存在，自动创建新用户
      if (!user) {
        console.log('Creating new user for email:', email);

        // 验证邀请码（如果提供）
        let isValidInvite = false;
        if (inviteCode && inviteCode.trim() !== '') {
          isValidInvite = await referralService.validateReferralCode(inviteCode.trim());
          if (!isValidInvite) {
            return response.status(400).json({
              success: false,
              message: '邀请码无效或已过期'
            });
          }
        }

        // 从邮箱提取用户名
        const username = email.split('@')[0];

        user = await prisma.user.create({
          data: {
            email,
            name: username,
            password: '', // 验证码登录不需要密码
            balance: {
              create: {
                mockInterviewCredits: 2, // 赠送2次模拟面试
                formalInterviewCredits: 0, // 正式面试初始为0次
                mianshijunBalance: 100  // 赠送100面巾
              }
            }
          },
          include: { balance: true }
        });

        console.log('New user created:', user.id);

        // 如果有有效邀请码，创建邀请关系
        if (isValidInvite && inviteCode) {
          try {
            await referralService.createReferralRelation(inviteCode.trim(), user.id);
            console.log(`✅ 邀请关系创建成功: 新用户 ${user.email} 通过邀请码 ${inviteCode} 注册`);
          } catch (error) {
            console.error('创建邀请关系失败:', error);
            // 不影响登录流程，只记录错误
          }
        }
      }

      // 生成JWT令牌
      const payload = {
        userId: user.id,
        email: user.email,
        name: user.name
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

      // 移除密码字段
      const { password: _, ...userWithoutPassword } = user;

      const responseObj = {
        success: true,
        message: '登录成功',
        data: {
          ...userWithoutPassword,
          token: token
        },
        token: token
      };

      return response.status(200).json(responseObj);
    }

    // 不支持的方法或路径
    return response.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });

  } catch (error) {
    console.error('验证码服务错误:', error);
    return response.status(500).json({ 
      success: false, 
      message: '服务器内部错误' 
    });
  }
}
