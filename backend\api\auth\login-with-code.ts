import type { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import VerificationService from '../../services/verificationService';

const loginSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  code: z.string().regex(/^\d{6}$/, '验证码必须是6位数字'),
  type: z.enum(['EMAIL', 'SMS'], { message: '验证码类型无效' })
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  const prisma = new PrismaClient();

  try {
    // 验证请求参数
    const { identifier, code, type } = loginSchema.parse(req.body);

    // 验证验证码
    const verificationService = new VerificationService();
    const verifyResult = await verificationService.verifyCode(identifier, code, type, 'LOGIN');

    if (!verifyResult.success) {
      return res.status(400).json({
        success: false,
        message: verifyResult.message,
        error: {
          code: verifyResult.isLocked ? 'ACCOUNT_LOCKED' : 'INVALID_CODE'
        }
      });
    }

    // 查找或创建用户
    let user = await prisma.user.findFirst({
      where: type === 'EMAIL'
        ? { email: identifier }
        : { phoneNumber: identifier },
      include: { balance: true }
    });

    // 如果用户不存在，自动创建新用户
    if (!user) {
      console.log('Creating new user for identifier:', identifier);

      // 从邮箱或手机号提取用户名
      const username = type === 'EMAIL'
        ? identifier.split('@')[0]
        : `用户${identifier.slice(-4)}`;

      user = await prisma.user.create({
        data: {
          email: type === 'EMAIL' ? identifier : undefined,
          phoneNumber: type === 'SMS' ? identifier : undefined,
          name: username,
          password: '', // 验证码登录不需要密码
          balance: {
            create: {
              mockInterviewCredits: 2, // 赠送2次模拟面试
              formalInterviewCredits: 0, // 正式面试初始为0次
              mianshijunBalance: 100 // 赠送100面巾
            }
          }
        },
        include: { balance: true }
      });

      console.log('New user created:', user.id);
    }

    // 生成JWT token
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // 返回登录成功响应
    const { password: _, ...userWithoutPassword } = user;

    return res.status(200).json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: userWithoutPassword,
        expiresIn: 24 * 60 * 60 // 24小时，单位秒
      }
    });

  } catch (error: any) {
    console.error('Login with code error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message,
        error: { code: 'VALIDATION_ERROR', details: error.errors }
      });
    }

    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: { code: 'INTERNAL_ERROR' }
    });
  } finally {
    await prisma.$disconnect();
  }
}
