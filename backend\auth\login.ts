import type { VercelRequest, VercelResponse } from '@vercel/node';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import type { Secret, SignOptions } from 'jsonwebtoken';
import dotenv from 'dotenv';
import prisma from '../lib/prisma'; // 使用共享的Prisma实例

// 加载环境变量
dotenv.config();

// 获取JWT密钥，如果不存在则使用默认值
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
// JWT过期时间，默认为24小时
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

export default async function handler(
  request: VercelRequest,
  response: VercelResponse,
) {
  if (request.method !== 'POST') {
    return response.status(405).json({ message: 'Only POST requests allowed' });
  }

  try {
    console.log('Login attempt:', request.body);
    const { email, password } = request.body;

    // 基本校验
    if (!email || !password) {
      console.log('Missing email or password');
      return response.status(400).json({ message: '邮箱和密码是必填项' });
    }

    console.log('Attempting to find user with email:', email);

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      include: { balance: true }, // 包含用户余额信息
    });

    // 用户不存在
    if (!user) {
      console.log('User not found with email:', email);
      return response.status(401).json({ message: '邮箱或密码不正确' });
    }

    console.log('User found, verifying password');

    // 验证密码
    console.log('Comparing passwords...');
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('Password validation failed');
      return response.status(401).json({ message: '邮箱或密码不正确' });
    }

    console.log('Password validated successfully, generating JWT token');

    // 登录成功，生成JWT令牌
    const { password: _, ...userWithoutPassword } = user;

    // 创建JWT负载
    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    // 生成JWT令牌
    console.log('JWT payload:', payload);
    console.log('JWT secret:', JWT_SECRET.substring(0, 3) + '...');
    console.log('JWT expires in:', JWT_EXPIRES_IN);

    let token;
    try {
      // 修复JWT签名类型问题
      // @ts-ignore - 使用 ts-ignore 来忽略类型检查，因为这里的类型定义与实际使用有冲突
      token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
      console.log('JWT token generated successfully');
    } catch (jwtError) {
      console.error('JWT token generation failed:', jwtError);
      throw jwtError;
    }

    // 确保token已经生成
    if (!token) {
      throw new Error('Token generation failed');
    }

    // 打印完整的响应对象，便于调试
    const responseObj = {
      success: true,
      message: '登录成功',
      data: {
        ...userWithoutPassword,
        token: token  // 将token放在data对象内部和外部都保留，增强兼容性
      },
      token: token  // 保留外部token字段以兼容现有前端代码
    };
    console.log('Sending successful response:', JSON.stringify(responseObj, null, 2));

    // 返回响应
    return response.status(200).json(responseObj);

  } catch (error) {
    console.error('登录错误:', error);
    // 详细记录错误信息
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      return response.status(500).json({ message: '服务器内部错误', error: error.message });
    }
    console.error('Unknown error type:', typeof error);
    return response.status(500).json({ message: '服务器内部错误' });
  } finally {
    // 注释掉 $disconnect，让连接池管理连接
    // await prisma.$disconnect(); // 关闭 Prisma Client 连接
  }
}
